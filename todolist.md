# TODO List - Microservice Demo Project (.NET Core API)

## 1. Khởi tạo dự án
### 1.1 Tạo solution .NET Core
- [x] Khởi tạo solution mới bằng lệnh `dotnet new sln` (Đ<PERSON> thực hiện: MicroserviceDemo.sln)
- [x] Đặt tên solution phù hợp với dự án (MicroserviceDemo)

### 1.2 Tạo các project API cho từng microservice
- [x] Tạo project WebAPI cho từng service: ProductService, OrderService, UserService (lệnh `dotnet new webapi`)
- [x] Thêm các project vào solution
- [ ] Đặt cấu trúc thư mục rõ ràng cho từng service

### 1.3 Tạo project Shared/Common
 [x] Tạo project class library dùng chung cho các model, DTO, helper
 [ ] Thêm reference từ các service đến project Shared/Common (sẽ thực hiện sau khi có model dùng chung)

## 2. Thi<PERSON><PERSON> kế kiến trúc
[x] Phân tích nghiệp vụ, x<PERSON><PERSON> định các domain chính (Product, Order, User)
[x] <PERSON><PERSON><PERSON> nghĩa ranh giới cho từng microservice (ProductService, OrderService, UserService)
 [x] Cài đặt Entity Framework Core cho từng service
 [ ] Cấu hình Entity Framework Core cho từng service
 [ ] Tạo migration và cập nhật database
 [ ] Thiết lập connection string bảo mật

### 2.2 Thiết kế giao tiếp giữa các service
 [ ] Lựa chọn phương thức giao tiếp: REST API, Message Queue
 [x] Cài đặt Swashbuckle.AspNetCore (Swagger) cho các service
 [ ] Thiết kế API contract (OpenAPI/Swagger)
 [ ] Xác định các sự kiện cần truyền qua message broker (nếu có)

### 2.3 Xây dựng các model dữ liệu
### 2.3 Xây dựng các model dữ liệu
- [x] Thiết kế các entity cho từng service (Product, Order, User)
- [x] Thiết kế DTO cho từng service (ProductDto, OrderDto, UserDto)
- [ ] Thiết kế ViewModel cho từng service
- [x] Đảm bảo tính nhất quán dữ liệu giữa các service (model dùng chung ở Shared)

## 3. Cài đặt các microservice
### 3.1 Tạo controller cho từng service
- [ ] Tạo các controller cho từng entity (Product, Order, User)
- [ ] Định nghĩa các route rõ ràng

### 3.2 Tạo các endpoint CRUD
- [ ] Tạo các action: Get, GetById, Create, Update, Delete
- [ ] Áp dụng các validation cho request

### 3.3 Kết nối database
- [ ] Cấu hình Entity Framework Core cho từng service
- [ ] Tạo migration và cập nhật database
- [ ] Thiết lập connection string bảo mật

### 3.4 Viết service logic
- [ ] Tách logic nghiệp vụ ra các service class
- [ ] Áp dụng Dependency Injection
- [ ] Xử lý exception và logging

## 4. Giao tiếp giữa các service
### 4.1 Tích hợp giao tiếp HTTP
- [ ] Sử dụng HttpClient hoặc Refit để gọi API giữa các service
- [ ] Xây dựng các client service cho việc gọi API

### 4.2 Tích hợp message broker (tuỳ chọn)
- [ ] Cài đặt RabbitMQ/Kafka
- [ ] Tạo producer/consumer cho các sự kiện cần truyền
- [ ] Xử lý các message nhận được

## 5. Authentication & Authorization
### 5.1 Tích hợp xác thực
- [ ] Cài đặt JWT hoặc IdentityServer
- [ ] Cấu hình middleware xác thực cho từng service

### 5.2 Phân quyền truy cập
- [ ] Định nghĩa các role và policy
- [ ] Áp dụng attribute [Authorize] cho các endpoint cần bảo vệ

## 6. Viết Unit Test & Integration Test
### 6.1 Viết Unit Test
- [ ] Viết test cho các controller (sử dụng xUnit, Moq)
- [ ] Viết test cho các service logic

### 6.2 Viết Integration Test
- [ ] Viết test cho các API endpoint
- [ ] Kiểm tra tích hợp giữa các service

## 7. Docker hóa dự án
### 7.1 Tạo Dockerfile cho từng service
- [ ] Viết Dockerfile cho từng project API
- [ ] Tối ưu image size

### 7.2 Tạo docker-compose
- [ ] Viết file docker-compose để chạy nhiều service cùng lúc
- [ ] Cấu hình network, volume cho các service

## 8. Tài liệu & Demo
### 8.1 Viết README
- [ ] Hướng dẫn cài đặt, chạy từng service
- [ ] Mô tả kiến trúc tổng thể

### 8.2 Chuẩn bị script khởi tạo dữ liệu mẫu
- [ ] Viết script seed dữ liệu cho database
- [ ] Tạo file Postman collection để test API

---
Bạn có thể bổ sung hoặc chỉnh sửa các bước tuỳ theo yêu cầu dự án.
